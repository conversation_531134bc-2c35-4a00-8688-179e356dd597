defmodule Drops.SQL.SqliteTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Sqlite

  @tag adapter: :sqlite
  test "introspect_table/2 returns valid AST for existing table", %{repo: repo} do
    {:ok, ast} = Sqlite.introspect_table("users", repo)

    # Verify AST structure
    assert {:table, {{:identifier, "users"}, columns, foreign_keys, indices}} = ast
    assert is_list(columns)
    assert is_list(foreign_keys)
    assert is_list(indices)

    # Verify we have expected columns
    column_names =
      Enum.map(columns, fn {:column, {{:identifier, name}, _type, _meta}} -> name end)

    assert "id" in column_names
    assert "name" in column_names
    assert "email" in column_names
  end

  @tag adapter: :sqlite
  test "introspect_table/2 returns error for non-existent table", %{repo: repo} do
    result = Sqlite.introspect_table("non_existent_table", repo)

    # SQLite might return empty results rather than error for non-existent tables
    # so we check that either we get an error or empty columns
    case result do
      {:ok, {:table, {{:identifier, "non_existent_table"}, columns, _fks, _indices}}} ->
        assert columns == []

      {:error, _reason} ->
        assert true
    end
  end
end
