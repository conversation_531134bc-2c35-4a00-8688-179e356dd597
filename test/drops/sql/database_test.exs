defmodule Drops.SQL.DatabaseTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database
  alias Drops.SQL.Database.Table

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "table/2 returns a complete table structure", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      assert %Table{} = table
      assert table.name == :users
      assert is_list(table.columns)
      assert length(table.columns) > 0
      assert %Database.PrimaryKey{} = table.primary_key
      assert is_list(table.foreign_keys)
      assert is_list(table.indices)
    end
  end
end
