defmodule Drops.SQL.DatabaseTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "table/2 introspects users table correctly", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Verify table name
      assert table.name == :users

      # Verify expected columns exist with correct types and metadata
      assert_column(table, :id, :integer, primary_key: true)
      assert_column(table, :name, :string, primary_key: false)
      assert_column(table, :email, :string, primary_key: false)

      # Debug: Check what columns are in the primary key
      IO.inspect(table.primary_key, label: "Primary Key")
      IO.inspect(Database.PrimaryKey.column_names(table.primary_key), label: "PK Column Names")

      # Verify primary key behavior
      assert Database.PrimaryKey.includes_column?(table.primary_key, "id") == true
      assert Database.PrimaryKey.includes_column?(table.primary_key, "name") == false
    end
  end
end
