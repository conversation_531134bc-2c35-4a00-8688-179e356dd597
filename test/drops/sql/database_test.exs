defmodule Drops.SQL.DatabaseTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "table/2 introspects users table correctly", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Verify table name
      assert table.name == :users

      # Verify expected columns exist with correct types
      assert table[:id] != nil
      assert table[:id].type == :integer
      assert table[:id].meta.primary_key == true

      assert table[:email] != nil
      assert table[:email].type == :string
      assert table[:email].meta.primary_key == false

      # Verify primary key behavior
      assert Database.PrimaryKey.includes_column?(table.primary_key, "id") == true
      assert Database.PrimaryKey.includes_column?(table.primary_key, "email") == false
    end
  end
end
