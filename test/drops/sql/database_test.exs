defmodule Drops.SQL.DatabaseTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database
  alias Drops.SQL.Database.Table

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "table/2 returns a complete table structure", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      assert %Table{} = table
      assert table.name == :users
      assert is_list(table.columns)
      assert length(table.columns) > 0
      assert %Database.PrimaryKey{} = table.primary_key
      assert is_list(table.foreign_keys)
      assert is_list(table.indices)
    end

    @tag relations: [:posts]
    test "table/2 handles tables with foreign keys", %{repo: repo} do
      {:ok, table} = Database.table("posts", repo)

      assert %Table{} = table
      assert table.name == :posts
      assert length(table.foreign_keys) > 0

      # Should have a foreign key to users table
      user_fk =
        Enum.find(table.foreign_keys, fn fk ->
          fk.referenced_table == "users"
        end)

      assert user_fk != nil
      assert "user_id" in user_fk.columns
    end

    test "table/2 returns error for non-existent table", %{repo: repo} do
      assert {:error, _reason} = Database.table("non_existent_table", repo)
    end
  end

  describe "compile_table/3" do
    @tag adapter: :sqlite
    test "compiles SQLite table AST to Table struct", %{repo: repo} do
      # Get a real AST from SQLite introspection
      {:ok, ast} = Drops.SQL.Sqlite.introspect_table("users", repo)

      {:ok, table} =
        Database.compile_table(
          Drops.SQL.Compilers.Sqlite,
          ast,
          %{adapter: :sqlite}
        )

      assert %Table{} = table
      assert table.name == :users
      assert table.adapter == :sqlite
      assert is_list(table.columns)
      assert length(table.columns) > 0
    end

    @tag adapter: :postgres
    test "compiles PostgreSQL table AST to Table struct", %{repo: repo} do
      # Get a real AST from PostgreSQL introspection
      {:ok, ast} = Drops.SQL.Postgres.introspect_table("users", repo)

      {:ok, table} =
        Database.compile_table(
          Drops.SQL.Compilers.Postgres,
          ast,
          %{adapter: :postgres}
        )

      assert %Table{} = table
      assert table.name == :users
      assert table.adapter == :postgres
      assert is_list(table.columns)
      assert length(table.columns) > 0
    end

    test "returns error for invalid AST" do
      invalid_ast = {:invalid, "not a valid AST"}

      result =
        Database.compile_table(
          Drops.SQL.Compilers.Sqlite,
          invalid_ast,
          %{adapter: :sqlite}
        )

      assert {:error, _reason} = result
    end
  end

  describe "adapter detection" do
    test "detects SQLite adapter correctly" do
      # This tests the private get_database_adapter/1 function indirectly
      # by using a SQLite repo
      repo = Drops.Relation.Repos.Sqlite

      {:ok, table} = Database.table("users", repo)
      assert table.adapter == :sqlite
    end

    test "detects PostgreSQL adapter correctly" do
      # This tests the private get_database_adapter/1 function indirectly
      # by using a PostgreSQL repo
      repo = Drops.Relation.Repos.Postgres

      {:ok, table} = Database.table("users", repo)
      assert table.adapter == :postgres
    end
  end

  describe "introspect_table/2 callback" do
    @tag adapter: :sqlite
    test "SQLite adapter implements introspect_table/2", %{repo: repo} do
      {:ok, ast} = Drops.SQL.Sqlite.introspect_table("users", repo)

      assert {:table, {{:identifier, "users"}, columns, foreign_keys, indices}} = ast
      assert is_list(columns)
      assert is_list(foreign_keys)
      assert is_list(indices)
    end

    @tag adapter: :postgres
    test "PostgreSQL adapter implements introspect_table/2", %{repo: repo} do
      {:ok, ast} = Drops.SQL.Postgres.introspect_table("users", repo)

      assert {:table, {{:identifier, "users"}, columns, foreign_keys, indices}} = ast
      assert is_list(columns)
      assert is_list(foreign_keys)
      assert is_list(indices)
    end
  end

  describe "adapter functions" do
    test "SQLite adapter returns correct opts and adapter" do
      assert Drops.SQL.Sqlite.opts() == [adapter: :sqlite, compiler: Drops.SQL.Compilers.Sqlite]
      assert Drops.SQL.Sqlite.adapter() == :sqlite
    end

    test "PostgreSQL adapter returns correct opts and adapter" do
      assert Drops.SQL.Postgres.opts() == [
               adapter: :postgres,
               compiler: Drops.SQL.Compilers.Postgres
             ]

      assert Drops.SQL.Postgres.adapter() == :postgres
    end
  end
end
