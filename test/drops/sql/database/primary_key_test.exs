defmodule Drops.SQL.Database.PrimaryKeyTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "includes_column?/2 correctly identifies primary key columns", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Test that id column is included in primary key
      assert Database.PrimaryKey.includes_column?(table.primary_key, "id") == true

      # Test that a non-existent column is not included
      assert Database.PrimaryKey.includes_column?(table.primary_key, "non_existent") == false

      # Find a non-primary key column and test it's not included
      non_pk_column = Enum.find(table.columns, fn col ->
        not Database.Column.primary_key?(col)
      end)
      
      if non_pk_column do
        assert Database.PrimaryKey.includes_column?(table.primary_key, non_pk_column.name) == false
      end
    end

    @tag relations: [:users]
    test "from_columns/1 correctly extracts primary key columns", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Create a new primary key from the table's columns
      pk = Database.PrimaryKey.from_columns(table.columns)

      # Should have the same primary key columns as the original table
      assert length(pk.columns) == length(table.primary_key.columns)
      
      # All columns in the primary key should be marked as primary key
      Enum.each(pk.columns, fn col ->
        assert Database.Column.primary_key?(col) == true
      end)
    end
  end
end
