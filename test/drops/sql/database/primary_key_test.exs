defmodule Drops.SQL.Database.PrimaryKeyTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "includes_column?/2 identifies primary key membership correctly", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Test that id column is included in primary key
      assert Database.PrimaryKey.includes_column?(table.primary_key, "id") == true

      # Test that name column is not included in primary key
      assert Database.PrimaryKey.includes_column?(table.primary_key, "name") == false

      # Test that non-existent column is not included
      assert Database.PrimaryKey.includes_column?(table.primary_key, "non_existent") == false
    end

    @tag relations: [:users]
    test "from_columns/1 extracts primary key columns correctly", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Create a new primary key from the table's columns
      pk = Database.PrimaryKey.from_columns(table.columns)

      # Should identify the same primary key column
      assert Database.PrimaryKey.includes_column?(pk, "id") == true
      assert Database.PrimaryKey.includes_column?(pk, "name") == false
    end
  end
end
