defmodule Drops.SQL.Database.ColumnTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "primary_key?/1 correctly identifies primary key columns", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Find the id column (should be primary key)
      id_column = table[:id]
      assert id_column != nil
      assert Database.Column.primary_key?(id_column) == true

      # Find a non-primary key column
      non_pk_column = Enum.find(table.columns, fn col ->
        not Database.Column.primary_key?(col)
      end)
      
      if non_pk_column do
        assert Database.Column.primary_key?(non_pk_column) == false
      end
    end
  end
end
