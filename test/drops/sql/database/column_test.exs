defmodule Drops.SQL.Database.ColumnTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Database

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "primary_key?/1 identifies primary key columns correctly", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Test that id column is identified as primary key
      id_column = table[:id]
      assert Database.Column.primary_key?(id_column) == true

      # Test that name column is not identified as primary key
      name_column = table[:name]
      assert Database.Column.primary_key?(name_column) == false
    end
  end
end
