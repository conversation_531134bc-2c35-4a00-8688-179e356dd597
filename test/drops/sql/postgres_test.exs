defmodule Drops.SQL.PostgresTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Postgres

  @tag adapter: :postgres
  test "introspect_table/2 returns valid AST for existing table", %{repo: repo} do
    {:ok, ast} = Postgres.introspect_table("users", repo)

    # Verify AST structure
    assert {:table, {{:identifier, "users"}, columns, foreign_keys, indices}} = ast
    assert is_list(columns)
    assert is_list(foreign_keys)
    assert is_list(indices)

    # Verify we have expected columns
    column_names =
      Enum.map(columns, fn {:column, {{:identifier, name}, _type, _meta}} -> name end)

    assert "id" in column_names
    assert "name" in column_names
    assert "email" in column_names
  end

  @tag adapter: :postgres
  test "introspect_table/2 returns error for non-existent table", %{repo: repo} do
    result = Postgres.introspect_table("non_existent_table", repo)

    # PostgreSQL should return an error for non-existent tables
    assert {:error, _reason} = result
  end
end
