defmodule Drops.SQL.Compilers.SqliteTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Compilers.Sqlite
  alias Drops.SQL.Database

  @tag adapter: :sqlite
  test "process/2 compiles SQLite table AST correctly", %{repo: repo} do
    # Get a real AST from SQLite introspection
    {:ok, ast} = Drops.SQL.Sqlite.introspect_table("users", repo)

    # Process the AST using the SQLite compiler
    table = Sqlite.process(ast, %{adapter: :sqlite})

    # Verify the compiled table has correct structure and types
    assert table.name == :users
    assert table.adapter == :sqlite

    # Verify SQLite-specific type mappings
    id_column = table[:id]
    assert id_column.type == :integer  # SQLite INTEGER -> :integer

    email_column = table[:email]
    assert email_column.type == :string  # SQLite TEXT -> :string
  end
end
