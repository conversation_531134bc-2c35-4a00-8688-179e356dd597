defmodule Drops.SQL.Compilers.PostgresTest do
  use Drops.RelationCase, async: false

  alias Drops.SQL.Compilers.Postgres
  alias Drops.SQL.Database

  @tag adapter: :postgres
  test "process/2 compiles PostgreSQL table AST correctly", %{repo: repo} do
    # Get a real AST from PostgreSQL introspection
    {:ok, ast} = Drops.SQL.Postgres.introspect_table("users", repo)

    # Process the AST using the PostgreSQL compiler
    table = Postgres.process(ast, %{adapter: :postgres})

    # Verify the compiled table has correct structure and types
    assert table.name == :users
    assert table.adapter == :postgres

    # Verify PostgreSQL-specific type mappings
    # PostgreSQL int4 -> :integer
    assert_column(table, :id, :integer)
    # PostgreSQL text -> :string
    assert_column(table, :name, :string)
    assert_column(table, :email, :string)
  end
end
